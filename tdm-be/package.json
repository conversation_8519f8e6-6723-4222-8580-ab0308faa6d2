{"name": "tdm-be", "private": true, "engines": {"node": "18"}, "contributors": [{"name": "<PERSON> (AlasDiablo) - Maintainer", "url": "https://github.com/AlasDiablo"}], "scripts": {"dev": "nodemon src/app.ts", "start": "ts-node --transpile-only ./src/app.ts", "swagger-autogen": "ts-node --project tsconfig.dev.json bin/swagger.ts 3000", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint:test": "eslint \"{src,apps,libs,test}/**/*.ts\""}, "keywords": [], "author": "", "license": "CeCILL", "dependencies": {"axios": "^1.7.4", "better-sqlite3": "^11.1.2", "config": "^3.3.12", "cors": "^2.8.5", "express": "^4.19.2", "express-basic-auth": "^1.2.1", "express-rate-limit": "^7.3.1", "md5": "^2.3.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.14", "nunjucks": "^3.2.4", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.2.2", "winston": "^3.13.1"}, "devDependencies": {"@draconides/eslint-config-ts": "^1.4.1", "@playwright/test": "^1.54.0", "@types/better-sqlite3": "^7.6.11", "@types/config": "^3.3.4", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/md5": "^2.3.5", "@types/multer": "^1.4.11", "@types/node": "^18.19.39", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.15", "@types/nunjucks": "^3.2.6", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "eslint": "^8.57.0", "nodemon": "^3.1.4"}}