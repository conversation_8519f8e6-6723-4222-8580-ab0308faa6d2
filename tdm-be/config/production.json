{"password": "{very secure password}", "hosts": {"internal": {"host": "{machine ip or internal host}", "isHttps": false}, "external": {"host": "{external host / revers proxy host}", "isHttps": true}}, "mailFrom": "\"ISTEX TDM Factory\" <<EMAIL>>", "smtp": {"host": "{mail services host}", "port": 1025, "secure": false, "tls": {"ignore": true, "rejectUnauthorized": false}}, "cron": {"schedule": "0 0 * * *", "deleteFileOlderThan": 7}, "inputFormat2labels": {"csv": {"summary": "Tableur `.csv`", "description": "Un fichier tableur au format `.csv` contenant des colonnes avec une entête. L'encodage doit être UTF-8. Le délimiteur est détecté automatiquement.", "extensions": ["csv"]}, "istex.tar.gz": {"summary": "Corpus Istex `.tar.gz`", "description": "Un corpus déchargé d'[ISTEX Search](https://search.istex.fr) au format `.tar.gz` (à choisir dans *Format de l'archive*). Il doit contenir **des métadonnées JSON** (par exemple en sélectionnant l'usage LODEX).\n\nIl est préférable qu'il contienne les `abstract`s.", "extensions": ["tar.gz", "tgz"]}, "pdf": {"summary": "PDF", "description": "Fichier PDF texte. Le PDF ne doit pas être un PDF image.", "extensions": ["pdf"]}, "tei-persee.tar.gz": {"summary": "Corpus TEI Persée `.tar.gz`", "description": "Un corpus contenant des fichiers XML-TEI Persée dans un répertoire `data`, au format `.tar.gz`.", "extensions": ["tar.gz", "tgz"]}, "txt": {"summary": "Texte `.txt`", "description": "Un fichier texte brut, encodé en UTF-8.", "extensions": ["txt"]}, "txt.tar.gz": {"summary": "Corpus de textes `.tar.gz`", "description": "Un corpus contenant des fichiers texte, encodés en UTF-8, dans un répertoire `data`, au format `.tar.gz`.", "extensions": ["tar.gz", "tgz"]}, "*": {"summary": "Peu importe", "description": "N'importe quel format.", "extensions": ["*"]}}, "flows": [{"id": "txt-aiabstract-check", "featured": false, "input": "article", "inputFormat": "txt", "wrapper": "/v1/txt", "enricher": "https://data-workflow.services.istex.fr/v1/aiabstract-check", "retrieve": "/v1/retrieve-csv", "retrieveExtension": "csv", "summary": "**aiAbstractCheck** - Détection d'abstract généré par IA", "description": "Indique si un résumé scientifique en anglais a été généré par IA ou non, ainsi que son score associée.", "descriptionLink": "https://services.istex.fr/detection-de-resume-scientifique-genere-par-ia/"}, {"id": "istex-aiabstract-check", "featured": false, "input": "corpus", "inputFormat": "istex.tar.gz", "wrapper": "/v1/istex-tar-gz", "enricher": "https://data-workflow.services.istex.fr/v1/aiabstract-check", "retrieve": "/v1/retrieve-csv", "retrieveExtension": "csv", "summary": "**aiAbstractCheck** - Détection d'abstracts générés par IA", "description": "Indique si les résumés scientifiques en anglais ont été générés par IA ou non, ainsi que leurs scores associés.", "descriptionLink": "https://services.istex.fr/detection-de-resume-scientifique-genere-par-ia/"}, {"id": "pdf-astro-tag", "featured": false, "input": "article", "inputFormat": "pdf", "wrapper": "/v1/pdf", "enricher": "https://data-workflow.services.istex.fr/v1/astro-tag-pdf", "retrieve": "/v1/retrieve-csv", "retrieveExtension": "csv", "summary": "**astroTag** - Extraction d'entités astronomiques", "description": "Extrait les entités astronomiques d'un article en PDF.", "descriptionLink": "https://services.istex.fr/detection-dentites-nommees-en-astronomie/"}, {"id": "pdf-bibcheck", "featured": true, "input": "article", "inputFormat": "pdf", "wrapper": "/v1/pdf", "enricher": "https://data-workflow.services.istex.fr/v1/bibcheck-pdf", "retrieve": "/v1/retrieve-csv", "retrieveExtension": "csv", "summary": "**bibCheck** - Contrôle de références bibliographiques", "description": "Contrôle les références bibliographiques d'un article en PDF, en vérifiant leur présence dans Crossref tout en s'assurant que l'article associé n'est pas rétracté.", "descriptionLink": "https://services.istex.fr/validation-de-reference-bibliographique/"}, {"id": "pdf-chem-tag", "featured": false, "input": "article", "inputFormat": "pdf", "wrapper": "/v1/pdf", "enricher": "https://data-workflow.services.istex.fr/v1/chem-tag-pdf", "retrieve": "/v1/retrieve-csv", "retrieveExtension": "csv", "summary": "**chemTag** - Extraction d'entités chimiques", "description": "Extrait les entités chimiques d'un article en PDF.", "descriptionLink": "https://services.istex.fr/detection-dentites-nommees-en-chimie/"}, {"id": "pdf-datatable-extract", "featured": false, "input": "article", "inputFormat": "pdf", "wrapper": "/v1/pdf", "enricher": "https://data-table.services.istex.fr/v1/table-extraction", "retrieve": "/v1/retrieve-csv", "retrieveExtension": "csv", "summary": "**datatableExtract** - Extraction de tableaux", "description": "Extrait les tableaux d'un article en PDF (de préférence en anglais).", "descriptionLink": "https://services.istex.fr/detection-et-extraction-de-tableaux-dans-un-article-scientifique"}, {"id": "csv-lda-en", "featured": false, "input": "corpus", "inputFormat": "csv", "wrapper": "/v1/csv", "wrapperParameter": "value", "wrapperParameterComplete": "/v1/fields/csv", "enricher": "https://data-computer.services.istex.fr/v1/lda", "retrieve": "/v1/retrieve-csv", "retrieveExtension": "csv", "summary": "**ldaClass** - Extrait des thématiques d'un corpus", "description": "Extrait des thématiques d’un corpus de textes écrits en anglais : une thématique (ou topic) est caractérisée par dix mots. Une fois les thématiques extraites, chaque document se voit attribuer une ou plusieurs thématique(s).", "descriptionLink": "https://services.istex.fr/creation-de-topics-sur-un-corpus-lda/"}, {"id": "istex-lda-en", "featured": false, "input": "corpus", "inputFormat": "istex.tar.gz", "wrapper": "/v1/istex-tar-gz", "wrapperParameter": "abstract", "enricher": "https://data-computer.services.istex.fr/v1/lda", "retrieve": "/v1/retrieve-csv", "retrieveExtension": "csv", "summary": "**ldaClass** - Extrait des thématiques d'un corpus", "description": "Extrait des thématiques d’un corpus de textes écrits en anglais : une thématique (ou topic) est caractérisée par dix mots. Une fois les thématiques extraites, chaque document se voit attribuer une ou plusieurs thématique(s).", "descriptionLink": "https://services.istex.fr/creation-de-topics-sur-un-corpus-lda/"}, {"id": "csv-noise-en", "featured": false, "input": "corpus", "inputFormat": "csv", "wrapper": "/v1/csv", "wrapperParameter": "value", "wrapperParameterComplete": "/v1/fields/csv", "enricher": "https://text-clustering.services.istex.fr/v1/noise", "retrieve": "/v1/retrieve-csv", "retrieveExtension": "csv", "summary": "**noiseDetect** - Détection du bruit", "description": "Identifie les documents non pertinents dans un corpus thématique.\n\nUtilise un algorithme de *clustering* sur des résumés écrits en anglais et retourne uniquement la liste des identifiants considérés comme du bruit.\n\nLe fichier `.csv` devra comporter une colonne `id` permettant d'identifier les données.", "descriptionLink": "https://services.istex.fr/detection-de-bruit-dun-corpus/"}, {"id": "istex-noise-en", "featured": false, "input": "corpus", "inputFormat": "istex.tar.gz", "wrapper": "/v1/istex-tar-gz", "wrapperParameter": "abstract", "enricher": "https://text-clustering.services.istex.fr/v1/noise", "retrieve": "/v1/retrieve-csv", "retrieveExtension": "csv", "summary": "**noiseDetect** - Détection du bruit", "description": "Identifie les documents non pertinents dans un corpus thématique.\n\nUtilise un algorithme de *clustering* sur des résumés écrits en anglais et retourne uniquement la liste des identifiants considérés comme du bruit.", "descriptionLink": "https://services.istex.fr/detection-de-bruit-dun-corpus/"}, {"id": "tei-rapido-algorithme", "featured": false, "input": "corpus", "inputFormat": "tei-persee.tar.gz", "wrapper": "/v1/no-convert", "enricher": "https://data-rapido.services.istex.fr/v1/rapido-algorithme", "retrieve": "/v1/retrieve-json", "retrieveExtension": "json", "summary": "**Rapido phase 1** - Recherche d'entités et d'alignement avec idRef, projet Rapido", "description": "Prend en entrée un `.tar.gz` comportant un dossier `data` incluant tous les documents xml à traiter. Il renvoie un json comportant les alignements que l'algorithme a pu faire entre le texte et le référentiel idRef."}, {"id": "tei-rapido-apprentissage", "featured": false, "input": "corpus", "inputFormat": "tei-persee.tar.gz", "wrapper": "/v1/no-convert", "enricher": "https://data-rapido.services.istex.fr/v1/rapido-apprentissage", "retrieve": "/v1/retrieve-json", "retrieveExtension": "json", "summary": "**Rapido phase 2** - Détecte des entités par apprentissage, puis aligne avec idRef, projet Rapido", "description": "Prend en entrée un `.tar.gz` comportant un dossier `data` incluant tous les documents xml à traiter. Il renvoie un json comportant les alignements que l'algorithme a pu faire entre le texte et le référentiel idRef."}, {"id": "csv-tag-cloud-en", "featured": true, "input": "corpus", "inputFormat": "csv", "wrapper": "/v1/csv", "wrapperParameter": "value", "wrapperParameterComplete": "/v1/fields/csv", "enricher": "https://data-workflow.services.istex.fr/v1/tag-cloud-en", "retrieve": "/v1/retrieve-csv", "retrieveExtension": "csv", "summary": "**tagCloud** - Extrait des termes pertinents de textes en anglais", "description": "Extrait les termes les plus spécifiques de chacun des textes en anglais (avec l'algorithme Teeft). Associe chaque terme au nombre de documents dans lequel il apparaît.\n\nPermet de créer des nuages de mots.\n\nL'entrée est un fichier CSV avec une colonne de texte en anglais. On peut saisir le nom de la colonne à traiter.", "descriptionLink": "https://services.istex.fr/extraction-de-termes-teeft/"}, {"id": "csv-tag-cloud-fr", "featured": false, "input": "corpus", "inputFormat": "csv", "wrapper": "/v1/csv", "wrapperParameter": "value", "wrapperParameterComplete": "/v1/fields/csv", "enricher": "https://data-workflow.services.istex.fr/v1/tag-cloud-fr", "retrieve": "/v1/retrieve-csv", "retrieveExtension": "csv", "summary": "**tagCloud** - Extrait des termes pertinents de textes en français", "description": "Extrait les termes les plus spécifiques de chacun des textes en français (avec l'algorithme Teeft). Associe chaque terme au nombre de documents dans lequel il apparaît.\n\nPermet de créer des nuages de mots.\n\nL'entrée est un fichier CSV avec une colonne de texte en français. On peut saisir le nom de la colonne à traiter.", "descriptionLink": "https://services.istex.fr/extraction-de-termes-teeft/"}, {"id": "istex-tag-cloud-en", "featured": false, "input": "corpus", "inputFormat": "istex.tar.gz", "wrapper": "/v1/istex-tar-gz", "wrapperParameter": "abstract", "enricher": "https://data-workflow.services.istex.fr/v1/tag-cloud-en", "retrieve": "/v1/retrieve-csv", "retrieveExtension": "csv", "summary": "**tagCloud** - Extrait des termes pertinents de textes en anglais", "description": "Extrait les termes les plus spécifiques de chacun des textes en anglais (avec l'algorithme Teeft). Associe chaque terme au nombre de documents dans lequel il apparaît.\n\nPermet de créer des nuages de mots.\n\nL'entrée est un fichier corpus de ISTEX Search contenant les métadonnées JSON. Ce sont les *abstract*s qui seront analysés.", "descriptionLink": "https://services.istex.fr/extraction-de-termes-teeft/"}, {"id": "istex-tag-cloud-fr", "featured": false, "input": "corpus", "inputFormat": "istex.tar.gz", "wrapper": "/v1/istex-tar-gz", "wrapperParameter": "abstract", "enricher": "https://data-workflow.services.istex.fr/v1/tag-cloud-fr", "retrieve": "/v1/retrieve-csv", "retrieveExtension": "csv", "summary": "**tagCloud** - Extrait des termes pertinents de textes en français", "description": "Extrait les termes les plus spécifiques de chacun des *abstract*s en français (avec l'algorithme Teeft). Associe chaque terme au nombre de documents dans lequel il apparaît.\n\nPermet de créer des nuages de mots.\n\nL'entrée est un fichier corpus de ISTEX Search contenant les métadonnées JSON. Ce sont les *abstract*s qui seront analysés.", "descriptionLink": "https://services.istex.fr/extraction-de-termes-teeft/"}, {"id": "txt-teeft-fr", "featured": false, "input": "article", "inputFormat": "txt", "wrapper": "/v1/txt", "enricher": "https://data-workflow.services.istex.fr/v1/teeft-fr", "retrieve": "/v1/retrieve-lines-csv", "retrieveExtension": "csv", "summary": "**Teeft** - Extrait des termes d'un texte en français", "description": "Extrait les 10 termes les plus spécifiques d'un texte en français. Permet d'avoir une idée de ce dont il est question dans le texte.", "descriptionLink": "https://services.istex.fr/extraction-de-termes-teeft/"}, {"id": "txt-teeft-en", "featured": false, "input": "article", "inputFormat": "txt", "wrapper": "/v1/txt", "enricher": "https://data-workflow.services.istex.fr/v1/teeft-en", "retrieve": "/v1/retrieve-lines-csv", "retrieveExtension": "csv", "summary": "**Teeft** - Extrait des termes d'un texte en anglais", "description": "Extrait les 10 termes les plus spécifiques d'un texte en anglais. Permet d'avoir une idée de ce dont il est question dans le texte.", "descriptionLink": "https://services.istex.fr/extraction-de-termes-teeft/"}, {"id": "istex-teeft-en", "featured": false, "input": "corpus", "inputFormat": "istex.tar.gz", "wrapper": "/v1/istex-tar-gz", "wrapperParameter": "abstract", "enricher": "https://data-workflow.services.istex.fr/v1/teeft-en", "retrieve": "/v1/retrieve-lines-csv", "retrieveExtension": "csv", "summary": "**Teeft** - Extrait des termes pertinents de textes en anglais", "description": "Extrait les 10 termes les plus spécifiques de chacun des textes en anglais. Permet d'avoir une idée de ce dont il est question dans chaque texte.\n\nL'entrée est un fichier corpus de ISTEX Search contenant les métadonnées JSON. Ce sont les *abstract*s qui seront analysés.", "descriptionLink": "https://services.istex.fr/extraction-de-termes-teeft/"}, {"id": "istex-teeft-fr", "featured": false, "input": "corpus", "inputFormat": "istex.tar.gz", "wrapper": "/v1/istex-tar-gz", "wrapperParameter": "abstract", "enricher": "https://data-workflow.services.istex.fr/v1/teeft-fr", "retrieve": "/v1/retrieve-lines-csv", "retrieveExtension": "csv", "summary": "**Teeft** - Extrait des termes pertinents de textes en français", "description": "Extrait les 10 termes les plus spécifiques de chacun des textes en français. Permet d'avoir une idée de ce dont il est question dans chaque texte.\n\nL'entrée est un fichier corpus de ISTEX Search contenant les métadonnées JSON. Ce sont les *abstract*s qui seront analysés.", "descriptionLink": "https://services.istex.fr/extraction-de-termes-teeft/"}, {"id": "csv-teeft-en", "featured": false, "input": "corpus", "inputFormat": "csv", "wrapper": "/v1/csv", "wrapperParameter": "value", "wrapperParameterComplete": "/v1/fields/csv", "enricher": "https://data-workflow.services.istex.fr/v1/teeft-en", "retrieve": "/v1/retrieve-lines-csv", "retrieveExtension": "csv", "summary": "**Teeft** - Extrait des termes pertinents de textes en anglais", "description": "Extrait les 10 termes les plus spécifiques de chacun des textes en anglais. Permet d'avoir une idée de ce dont il est question dans chaque texte.\n\nL'entrée est un fichier CSV avec une colonne de texte en anglais. Par défaut c'est la colonne `abstract` qui est prise. On peut sélectionner le nom de la colonne à traiter.", "descriptionLink": "https://services.istex.fr/extraction-de-termes-teeft/"}, {"id": "csv-teeft-fr", "featured": false, "input": "corpus", "inputFormat": "csv", "wrapper": "/v1/csv", "wrapperParameter": "value", "wrapperParameterComplete": "/v1/fields/csv", "enricher": "https://data-workflow.services.istex.fr/v1/teeft-fr", "retrieve": "/v1/retrieve-lines-csv", "retrieveExtension": "csv", "summary": "**Teeft** - Extrait des termes pertinents de textes en français", "description": "Extrait les 10 termes les plus spécifiques de chacun des textes en français. Permet d'avoir une idée de ce dont il est question dans chaque texte.\n\nL'entrée est un fichier CSV avec une colonne de texte en français. Par défaut c'est la colonne `abstract` qui est prise. On peut sélectionner le nom de la colonne à traiter.", "descriptionLink": "https://services.istex.fr/extraction-de-termes-teeft/"}, {"id": "istex-termsuite-en", "featured": true, "input": "corpus", "inputFormat": "istex.tar.gz", "wrapper": "/v1/istex-tar-gz", "wrapperParameter": "abstract", "enricher": "https://data-termsuite.services.istex.fr/v1/en", "retrieve": "/v1/retrieve-csv", "retrieveExtension": "csv", "summary": "**TermSuite EN** - Extraction terminologique en anglais", "description": "Extraction de termes à partir d'un corpus ISTEX (en anglais).", "descriptionLink": "https://services.istex.fr/extraction-de-termes-dun-corpus/"}, {"id": "istex-termsuite-fr", "featured": false, "input": "corpus", "inputFormat": "istex.tar.gz", "wrapper": "/v1/istex-tar-gz", "wrapperParameter": "abstract", "enricher": "https://data-termsuite.services.istex.fr/v1/fr", "retrieve": "/v1/retrieve-csv", "retrieveExtension": "csv", "summary": "**TermSuite FR** - Extraction terminologique en français", "description": "Extraction de termes à partir d'un corpus ISTEX (en français).", "descriptionLink": "https://services.istex.fr/extraction-de-termes-dun-corpus/"}, {"id": "txttar-termsuite-en", "featured": true, "input": "corpus", "inputFormat": "txt.tar.gz", "wrapper": "/v1/tar-txt2json", "enricher": "https://data-termsuite.services.istex.fr/v1/en", "retrieve": "/v1/retrieve-csv", "retrieveExtension": "csv", "summary": "**TermSuite EN** - Extraction terminologique en anglais", "description": "Extraction de termes à partir d'un ensemble de fichiers texte (en anglais).", "descriptionLink": "https://services.istex.fr/extraction-de-termes-dun-corpus/"}, {"id": "txttar-termsuite-fr", "featured": false, "input": "corpus", "inputFormat": "txt.tar.gz", "wrapper": "/v1/tar-txt2json", "enricher": "https://data-termsuite.services.istex.fr/v1/fr", "retrieve": "/v1/retrieve-csv", "retrieveExtension": "csv", "summary": "**TermSuite FR** - Extraction terminologique en français", "description": "Extraction de termes à partir d'un ensemble de fichiers texte (en français).", "descriptionLink": "https://services.istex.fr/extraction-de-termes-dun-corpus/"}, {"id": "csv-text-clustering-en", "featured": true, "input": "corpus", "inputFormat": "csv", "wrapper": "/v1/csv", "wrapperParameter": "value", "wrapperParameterComplete": "/v1/fields/csv", "enricher": "https://text-clustering.services.istex.fr/v1/clustering", "retrieve": "/v1/retrieve-csv", "retrieveExtension": "csv", "summary": "**textClustering** - Classification de textes", "description": "Crée plusieurs groupes afin d'y classifier les différents textes en fonction de leur similarité. Les textes doivent être en anglais.", "descriptionLink": "https://services.istex.fr/extraction-de-cluster-dun-corpus/"}, {"id": "istex-text-clustering-en", "featured": true, "input": "corpus", "inputFormat": "istex.tar.gz", "wrapper": "/v1/istex-tar-gz", "wrapperParameter": "abstract", "enricher": "https://text-clustering.services.istex.fr/v1/clustering", "retrieve": "/v1/retrieve-csv", "retrieveExtension": "csv", "summary": "**textClustering** - Classification de textes", "description": "Crée plusieurs groupes afin d'y classifier les différents textes en fonction de leur similarité. Les textes doivent être en anglais.", "descriptionLink": "https://services.istex.fr/extraction-de-cluster-dun-corpus/"}, {"id": "pdf-text-extract", "featured": false, "input": "article", "inputFormat": "pdf", "wrapper": "/v1/pdf", "enricher": "https://data-workflow.services.istex.fr/v1/pdf-text", "retrieve": "/v1/retrieve-txt", "retrieveExtension": "txt", "summary": "**textExtract** - Transforme un PDF en texte", "description": "Transforme un PDF en texte en excluant les éléments qui perturberaient un traitement de fouille de texte ultérieur.\n\nLe PDF ne doit pas être un PDF image.", "descriptionLink": "https://services.istex.fr/extraction-du-texte-epure-de-pdf/"}, {"id": "istex-text-similarity", "featured": true, "input": "corpus", "inputFormat": "istex.tar.gz", "wrapper": "/v1/istex-tar-gz", "wrapperParameter": "title", "enricher": "https://data-computer.services.istex.fr/v1/corpus-similarity", "retrieve": "/v1/retrieve-json", "retrieveExtension": "json", "summary": "**textSimilarity** - Similarité entre documents", "description": "Calcule la similarité entre documents à partir de leur titre, dans un corpus ISTEX.", "descriptionLink": "https://services.istex.fr/calcul-de-similarite-entre-les-documents-dun-corpus/"}, {"id": "pdf-text-summarize", "featured": false, "input": "article", "inputFormat": "pdf", "wrapper": "/v1/pdf", "enricher": "https://data-workflow.services.istex.fr/v1/text-summarize-pdf", "retrieve": "/v1/retrieve-txt", "retrieveExtension": "txt", "summary": "**textSummarize** - Résumé automatique d'un article scientifique", "description": "Génère par IA un résumé d'un article scientifique en anglais au format PDF.", "descriptionLink": "https://services.istex.fr/resume-automatique-dun-article-scientifique/"}, {"id": "txt-text-summarize", "featured": false, "input": "article", "inputFormat": "txt", "wrapper": "/v1/txt", "enricher": "https://data-workflow.services.istex.fr/v1/text-summarize", "retrieve": "/v1/retrieve-txt", "retrieveExtension": "txt", "summary": "**textSummarize** - Résumé automatique d'un article scientifique", "description": "Génère par IA un résumé d'un article scientifique au format PDF et écrit en anglais.", "descriptionLink": "https://services.istex.fr/resume-automatique-dun-article-scientifique/"}, {"id": "csv-toprefextract", "featured": false, "input": "corpus", "inputFormat": "csv", "wrapper": "/v1/csv", "wrapperParameter": "value", "wrapperParameterComplete": "/v1/fields/csv", "enricher": "https://data-topcitation.services.istex.fr/v1/topcitation", "retrieve": "/v1/retrieve-csv", "retrieveExtension": "csv", "summary": "**topRefExtract** - Extraction des références phares", "description": "À partir d'une liste de DOI, renvoie sous format csv les 10 références les plus citées du corpus.", "descriptionLink": "https://services.istex.fr/extraction-des-references-phares-dun-corpus/"}, {"id": "istex-toprefextract", "featured": false, "input": "corpus", "inputFormat": "istex.tar.gz", "wrapper": "/v1/istex-tar-gz", "wrapperParameter": "doi.0", "enricher": "https://data-topcitation.services.istex.fr/v1/topcitation", "retrieve": "/v1/retrieve-csv", "retrieveExtension": "csv", "summary": "**topRefExtract** - Extraction des références phares", "description": "À partir d'une liste de DOI, renvoie sous format csv les 10 références les plus citées du corpus.", "descriptionLink": "https://services.istex.fr/extraction-des-references-phares-dun-corpus/"}]}