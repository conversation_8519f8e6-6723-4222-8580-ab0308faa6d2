// User input error
export const ERROR_MESSAGE_WRAPPER_BAD_USER_INPUT = "Le format du fichier fourni n'était pas le bon.";

// Server Error
export const ERROR_MESSAGE_WRAPPER_UNEXPECTED_ERROR = "Le changement de format du fichier s'est mal passé.";

export const ERROR_MESSAGE_FILE_SYSTEM_ERROR = "Le traitement n'a pas pu lire le fichier après changement de format.";

export const ERROR_MESSAGE_WRAPPER_UNREACHABLE_ERROR = 'Le convertisseur est actuellement inaccessible.';

export const ERROR_MESSAGE_ENRICHMENT_UNREACHABLE_ERROR = "L'enrichissement est actuellement inaccessible.";

export const ERROR_MESSAGE_ENRICHMENT_PAYLOAD_NOT_ACCEPTED_ERROR =
    "L'enrichissement a refusé le traitement des données.";

export const ERROR_MESSAGE_ENRICHMENT_UNEXPECTED_ERROR = "L'enrichissement a rencontré une erreur inattendue.";

export const ERROR_MESSAGE_ENRICHMENT_HOOK_UNEXPECTED_ERROR =
    'La récupération des données a rencontré une erreur inattendue.';

export const ERROR_MESSAGE_ENRICHMENT_HOOK_UNREACHABLE_ERROR =
    'Le service de récupération des données est actuellement inaccessible.';

export const ERROR_MESSAGE_ENRICHMENT_HOOK_PAYLOAD_NOT_ACCEPTED_ERROR = 'La récupération des données a été refusée.';

export const ERROR_MESSAGE_ENRICHMENT_WEBHOOK_TIMEOUT =
    "Le service d'enrichissement n'a pas répondu dans le délai imparti";
