{"wrappers": [{"url": "https://data-wrapper.services.istex.fr", "tags": [{"name": "data-wrapper", "excluded": ["v1/fields/csv", "/v1/retrieve-csv"]}]}], "enrichments": [{"url": "https://data-computer.services.istex.fr", "retrieveUrl": {"url": "/v1/retrieve-csv", "fileExtension": "csv"}, "tags": [{"name": "data-computer", "excluded": ["/v1/collect", "/v1/retrieve", "/v1/retrieve-csv", "/v1/retrieve-json", "/v1/mock-error-async", "/v1/mock-error-sync", "/v1/graph-segment", "/v1/statistics", "/v1/tree-segment", "/v1/lda-segment"]}]}, {"url": "https://data-termsuite.services.istex.fr", "retrieveUrl": {"url": "/v1/retrieve-csv", "fileExtension": "csv"}, "tags": [{"name": "data-termsuite", "excluded": ["/v1/retrieve", "/v1/retrieve-csv", "/v1/retrieve-json"]}]}, {"url": "https://text-clustering.services.istex.fr", "retrieveUrl": {"url": "/v1/retrieve-json", "fileExtension": "json"}, "tags": [{"name": "clustering", "excluded": ["/v1/retrieve", "/v1/retrieve-csv", "/v1/retrieve-json", "/v1/noise-lodex", "/v1/clustering"]}]}, {"url": "https://text-clustering.services.istex.fr", "retrieveUrl": {"url": "/v1/retrieve-csv", "fileExtension": "csv"}, "tags": [{"name": "clustering", "excluded": ["/v1/retrieve", "/v1/retrieve-csv", "/v1/retrieve-json", "/v1/noise-lodex", "/v1/noise"]}]}, {"url": "https://data-topcitation.services.istex.fr", "retrieveUrl": {"url": "/v1/retrieve-json", "fileExtension": "json"}, "tags": [{"name": "data-topcitation", "excluded": ["/v1/retrieve", "/v1/retrieve-csv", "/v1/retrieve-json"]}]}, {"url": "https://data-thesesul.services.istex.fr", "retrieveUrl": {"url": "/v1/retrieve", "fileExtension": "tar.gz"}, "tags": [{"name": "sudoc", "excluded": ["/v1/retrieve", "/v1/retrieve-tests"]}]}, {"url": "https://data-rapido.services.istex.fr", "retrieveUrl": {"url": "/v1/retrieve-json", "fileExtension": "json"}, "tags": [{"name": "data-rapido", "excluded": ["/v1/retrieve", "/v1/retrieve-csv", "/v1/retrieve-json"]}]}, {"url": "https://data-workflow.services.istex.fr", "retrieveUrl": {"url": "/v1/retrieve-csv", "fileExtension": "csv"}, "tags": [{"name": "bibcheck-pdf", "excluded": []}]}, {"url": "https://data-workflow.services.istex.fr", "retrieveUrl": {"url": "/v1/retrieve-csv", "fileExtension": "csv"}, "tags": [{"name": "sciencemetrix-class", "excluded": []}]}, {"url": "https://data-workflow.services.istex.fr", "retrieveUrl": {"url": "/v1/retrieve-csv", "fileExtension": "csv"}, "tags": [{"name": "tag-cloud", "excluded": []}]}, {"url": "https://data-workflow.services.istex.fr", "retrieveUrl": {"url": "/v1/retrieve-txt", "fileExtension": "txt"}, "tags": [{"name": "pdf-text", "excluded": []}]}]}