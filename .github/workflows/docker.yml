name: 'Build and Push'

on:
  release:
    types: [published]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout from Git
        uses: actions/checkout@v4

      - name: <PERSON><PERSON> Login
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build Docker Image
        run: docker build --no-cache -t cnrsinist/tdm-factory:${{ github.ref_name }} .

      - name: Push Docker Image
        run: docker push cnrsinist/tdm-factory:${{ github.ref_name }}
