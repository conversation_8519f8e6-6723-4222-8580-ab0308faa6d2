{"name": "tdm-admin", "private": true, "engines": {"node": "18"}, "contributors": [{"name": "<PERSON> (AlasDiablo) - Original Author", "url": "https://github.com/AlasDiablo"}], "type": "module", "scripts": {"dev": "VITE_TDM_FACTORY_HOST=http://localhost:3000 vite --host", "build": "tsc && vite build", "build:dev": "VITE_TDM_FACTORY_HOST=http://localhost:3000 npm run build", "preview": "vite preview", "lint": "eslint \"{src,apps,libs,test}/**/*.{tsx,ts}\" --fix", "lint:test": "eslint \"{src,apps,libs,test}/**/*.{tsx,ts}\"", "stylelint": "stylelint \"{src,public}/**/*.scss\" --fix", "stylelint:test": "stylelint \"{src,public}/**/*.scss\""}, "dependencies": {"@draconides/format": "^1.0.3", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^5.16.1", "@mui/material": "^5.16.1", "@tanstack/eslint-plugin-query": "^5.51.1", "@tanstack/react-query": "^5.51.1", "lodash": "^4.17.21", "markdown-it": "^14.1.0", "react": "^18.3.1", "react-dom": "^18.3.1", "recharts": "^2.12.7"}, "devDependencies": {"@draconides/eslint-config-react": "^1.2.0", "@draconides/eslint-config-ts": "^1.4.1", "@tanstack/react-query-devtools": "^5.51.1", "@types/lodash": "^4.17.7", "@types/markdown-it": "^14.1.1", "@types/md5": "^2.3.5", "@types/node": "^18.19.39", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.57.0", "md5": "^2.3.0", "sass": "^1.77.8", "stylelint": "^16.7.0", "stylelint-config-standard-scss": "^13.1.0", "stylelint-config-twbs-bootstrap": "^14.2.0", "terser": "^5.31.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.2.2", "vite": "^5.3.3", "vite-plugin-eslint": "^1.8.1", "vite-plugin-stylelint": "^5.3.1", "vite-tsconfig-paths": "^4.3.2"}}