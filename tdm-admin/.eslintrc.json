{"parser": "@typescript-eslint/parser", "parserOptions": {"project": "tsconfig.eslint.json", "sourceType": "module"}, "settings": {"react": {"version": "detect"}, "import/resolver": {"typescript": {"project": "tsconfig.eslint.json"}}}, "root": true, "env": {"node": true, "jest": true, "browser": true}, "plugins": ["@tanstack/query"], "extends": ["@draconides/eslint-config-ts", "@draconides/eslint-config-react", "plugin:@tanstack/eslint-plugin-query/recommended"]}