# OpenAPI Documentation - JSON format (dot notation)
mimeType = application/json

post.operationId = post-v1-table-extraction
post.description = Extrait toutes les tables d'un fichier PDF en français
post.summary = Extrait les tables d'un PDF en français
post.tags.0 = data-table
post.requestBody.content.application/x-tar.schema.type = string
post.requestBody.content.application/x-tar.schema.format = binary
post.requestBody.required = true
post.responses.default.description = Informations permettant de récupérer les données le moment venu
post.parameters.0.description = Indenter le JSON résultant
post.parameters.0.in = query
post.parameters.0.name = indent
post.parameters.0.schema.type = boolean
post.parameters.1.description = URL pour signaler que le traitement est terminé
post.parameters.1.in = header
post.parameters.1.name = X-Webhook-Success
post.parameters.1.schema.type = string
post.parameters.1.schema.format = uri
post.parameters.1.required = false
post.parameters.2.description = URL pour signaler que le traitement a échoué
post.parameters.2.in = header
post.parameters.2.name = X-Webhook-Failure
post.parameters.2.schema.type = string
post.parameters.2.schema.format = uri
post.parameters.2.required = false
post.parameters.3.in = query
post.parameters.3.name = lang
post.parameters.3.schema.type = string
post.parameters.3.description = langue du document
post.parameters.4.in = query
post.parameters.4.name = format
post.parameters.4.schema.type = string
post.parameters.4.description = Format de sortie du tableau parmis : dict, list, series, split, records, index
[env]
path = generator
value = table-extraction

[use]
plugin = basics
plugin = spawn

# Step 1 (générique): Charger le fichier corpus
[delegate]
file = charger-pdf.cfg

# Step 2 (générique): Traiter de manière asynchrone les items reçus
[fork]
standalone = true
logger = logger.cfg

# Step 2.1 (spécifique): Lancer un calcul sur tous les items reçus

[fork/exec]
# command should be executable !
command = ./v1/table-extraction.py
args = fix('-p')
args = env('lang', "fra")
args = fix('-q')
args = env('format', "index")

# Step 2.2 (générique): Enregistrer le résultat et signaler que le traitement est fini
[fork/delegate]
file = recorder.cfg

# Step 3 : Renvoyer immédiatement un seul élément indiquant comment récupérer le résultat quand il sera prêt
[delegate]
file = recipient.cfg
