# ws-biblio-tools@5.0.1

Outils pour références bibliographiques

Accède à des bases bibliographiques variées

À noter: à la mise en production, il faut ajouter les clés `OPENALEX_API_KEY` et
`UNPAYWALL_API_KEY` dans l'environnement. Quand on utilise ezMaster, c'est en
modifiant la configuration du container.

Pour le développement: mettez ces variables d'environnement dans le fichier
`.env` du répertoire du service, sous la forme:

```sh
export OPENALEX_API_KEY=T1BFTkFMRVhfQVBJX0tFWQo
export UNPAYWALL_API_KEY=VU5QQVlXQUxMX0FQSV9LRVkK
```
