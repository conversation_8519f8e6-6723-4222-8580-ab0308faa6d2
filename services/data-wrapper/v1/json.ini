# Entrypoint output format
mimeType = application/gzip
extension = tar.gz

# OpenAPI Documentation - JSON format (dot notation)
post.operationId = post-v1-json
post.summary = Transformation d'un fichier JSON en fichier corpus
#'
post.description = Le fichier JSON est transformé en fichier corpus exploitable par un web service asynchrone
post.tags.0 = data-wrapper
post.requestBody.content.application/json.schema.type = string
post.requestBody.content.application/json.schema.format = binary
post.requestBody.required = true
post.responses.default.description = Fichier corpus au format tar.gz
post.responses.default.content.application/gzip.schema.type = string
post.responses.default.content.application/gzip.schema.format = binary
post.parameters.0.description = Nom du champ à exploiter comme identifiant de ligne
post.parameters.0.in = query
post.parameters.0.name = id
post.parameters.0.schema.type = string
post.parameters.0.schema.default = id
post.parameters.0.required = false
post.parameters.1.description = Nom du champ à exploiter comme identifiant de colonne
post.parameters.1.in = query
post.parameters.1.name = value
post.parameters.1.schema.type = string
post.parameters.1.schema.default = value
post.parameters.1.required = false
post.parameters.2.description = chaque ligne est réduite à un object contenant 2 champs (id, value)
post.parameters.2.in = query
post.parameters.2.name = slim
post.parameters.2.schema.type = boolean
post.parameters.2.schema.default = true
post.parameters.2.required = false

[env]
path = slim
value = env('slim').thru(x => (x === 'false' ? false : true))

[use]
plugin = basics

[JSONParse]

[assign]
path = id
value = get(env('id', 'id'))
path = value
value = get(env('value', 'value'))

[exchange]
value = self().thru(x => _.env(null, 'slim') ? _.pick(x, ['id', 'value']) : x)

# Don't return empty values, so that validate doesn't fail
[assign]
path = value
value = get('value').thru(v => v === '' ? 'n/a' : v)

# in the hope that all the lines look the same
[singleton]
[singleton/validate]
path = value
rule = required

[TARDump]
compress = true
manifest = fix({version: '1'})
manifest = fix({generator: 'v1/csv'})
manifest = fix({parameters: _.omit(_.env(), 'headers')})
manifest = fix({hostAgent: _.get(_.env(), 'headers.host')})
manifest = fix({userAgent: _.get(_.env(), 'headers.user-agent')})
