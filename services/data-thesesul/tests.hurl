# WARNING: This file was not generated, but manually written.
# DON'T OVERWRITE IT
# Use it to test:
# npx hurl --test data-computer/tests.hurl

##################################################################
# v1/sudoc
POST {{host}}/v1/sudoc
content-type: application/x-tar
x-hook: https://webhook.site/69300b22-a251-4c16-9905-f7ba218ae7e9
file,example.tar.gz;

HTTP 200
# Capture the computing token
[Captures]
computing_token: jsonpath "$[0].value"
[Asserts]
variable "computing_token" exists

POST {{host}}/v1/retrieve
content-type: application/json
[Options]
delay: 25000
```
[
	{
		"value":"{{computing_token}}"
	}
]
```

HTTP 200
Content-type: application/x-gzip
[Asserts]
bytes count > 3890000
bytes count < 3892000