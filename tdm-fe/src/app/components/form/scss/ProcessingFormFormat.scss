.form-content:has(.processing-form-format) {
  padding-bottom: 70px;
}

/* stylelint-disable selector-class-pattern */
.form-content .MuiTypography-h3 {
  font-family: "Open Sans", sans-serif !important;
  font-size: 15px;
  font-weight: 700;
  color: var(--dark-black);
}

.processing-form-format {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;

  .MuiFormControl-root {
    width: 90%;
  }

  .MuiRadioGroup-root {
    display: flex;
    flex-direction: column;
    gap: 15px;
    width: 100%;

    .format-container {
      position: relative;
      width: 100%;
      cursor: pointer;
      background-color: #fff;
      background-color: var(--white);
      border: 2px solid var(--white);
      border-radius: 4px;
      transition: all .2s ease;

      &:hover {
        border-color: var(--blue);
      }

      &.expanded {
        .arrow-icon {
          transform: translateY(-50%) rotate(180deg);
        }
      }
    }
  }

  .format-label-container {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    min-height: 48px;
    cursor: pointer;
    background-color: transparent;
  }

  .MuiFormControlLabel-root {
    flex: 1;
    width: 90%;
    padding: 8px 12px;
    margin: 0;
    cursor: pointer;

    .MuiRadio-root {
      padding: 4px;
      margin: 2px;
      cursor: pointer;
    }

    .MuiTypography-root {
      display: flex;
      align-items: center;
      padding: 4px;
      margin: 0;
      font-size: 1em;
      cursor: pointer;
    }
  }

  .arrow-icon {
    position: absolute;
    top: 50%;
    right: 16px;
    width: 24px;
    height: 24px;
    color: #458ca5;
    cursor: pointer;
    transition: transform .3s ease;
    transform: translateY(-50%) rotate(0deg);
  }
}

.MuiCollapse-root {
  background-color: transparent;
  border: none;

  .format-details {
    padding: 0 12px 12px;
    font-size: .9em;
    color: #0d0d0d;
  }
}
