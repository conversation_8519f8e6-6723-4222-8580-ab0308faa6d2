@import "../../../scss/colors";

.processing-form-upload {
  box-sizing: border-box;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  padding: 0 16px;
  padding-bottom: 54px;

  .upload-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    max-width: 800px;
    padding-bottom: 80px;
    margin: 0 auto;

    h3 {
      margin-bottom: 24px;
      font-family: "Open Sans", sans-serif !important;
      font-size: 15px;
      font-weight: 700;
      color: var(--dark-black);
    }

    .upload-zone {
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      padding: 32px 16px;
      cursor: pointer;
      background-color: var(--white);
      border: 2px dashed var(--grey);
      border-radius: 8px;
      transition: all .3s ease;

      @media (min-width: 768px) {
        padding: 32px;
      }

      &.dragging {
        background-color: rgba(var(--blue-rgb), .05);
        border-color: var(--blue);
        box-shadow: 0 0 20px rgba(var(--blue-rgb), .1);
        transform: scale(1.02);

        .file-icon {
          color: var(--blue);
          transform: scale(1.1);
          animation: bounce 1s infinite;
        }
      }

      .file-icon {
        margin-bottom: 16px;
        font-size: 48px;
        color: var(--grey);
        transition: all .3s ease;
      }

      p {
        margin-bottom: 16px;
        font-size: 15px;
        font-weight: 700;
        color: #0d0d0d;
        text-align: center;
      }

      button {
        padding: 8px 16px;
        font-size: 13px;
        background-color: var(--blue);
        transition: all .3s ease;

        &:hover {
          background-color: var(--blue-dark);
          box-shadow: 0 4px 8px rgba(0, 0, 0, .1);
          transform: translateY(-2px);
        }

        &:active {
          transform: translateY(0);
        }
      }

      &.has-file {
        min-height: unset;
        padding: 16px;
        cursor: default;
        background-color: var(--white);
        border: 1px solid var(--grey);

        @media (min-width: 768px) {
          padding: 16px 24px;
        }

        .file-info {
          display: flex;
          align-items: center;
          width: 100%;
        }
      }
    }

    .file-details {
      flex: 1;
      max-width: 70%;
      margin-right: 10px;
      overflow: hidden;
    }

    .file-name {
      display: block;
      overflow: hidden;
      font-size: 15px;
      font-weight: 600;
      color: #0d0d0d;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .file-info {
      display: flex;
      gap: 8px;
      align-items: center;
      justify-content: space-between;
      width: 100%;
    }

    .file-actions {
      display: flex;
      gap: 8px;
      align-items: center;
      margin-left: auto;
    }

    .file-size {
      font-size: 15px;
      color: #0d0d0d;
      white-space: nowrap;
    }

    .remove-file {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;
      width: 24px;
      min-width: unset !important;
      height: 24px;
      padding: 0 !important;
      color: #458ca5 !important;
      background: none !important;
      border: none;
      box-shadow: none !important;

      &:hover {
        color: var(--blue) !important;
        background: none !important;
        box-shadow: none !important;
      }
    }

    .error-message {
      width: 100%;
      margin-top: 16px;
      font-size: .9em;
      color: #b22f10;
      text-align: center;

      p {
        margin-bottom: 8px;
        font-weight: 600;
        color: #b22f10;
      }

      span {
        color: #b22f10;
      }
    }
  }
}

.file-name {
  font-size: 1em;
  font-weight: 700;
  color: #0d0d0d;
  word-break: break-word;
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}
