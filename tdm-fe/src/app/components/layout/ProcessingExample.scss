.processing-example {
  position: relative;
  right: 50%;
  left: 50%;
  width: 100vw;
  padding: 48px 0;
  margin-top: 60px;
  margin-right: -50vw;
  margin-left: -50vw;
  background-color: #f5f5f5;

  h2 {
    margin-bottom: 20px;
    font-size: 30px;
    font-weight: 700;
    color: #4a4a4a;
  }
  .steps-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
  }

  .step-card {
    padding: 24px;
    background: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, .1);

    &.active {
      border-top: solid #458ca5 10px;
    }

    .step-number {
      margin-bottom: 12px;
      font-size: 15px;
      font-weight: 600;
      color: #c4d733;
    }

    h3 {
      margin-bottom: 12px;
      font-size: 19px;
      font-weight: 700;
      color: #458ca5;
    }

    p {
      font-size: 15px;
      line-height: 1.5;
      color: #0d0d0d;
    }
  }
}

@media (max-width: 768px) {
  .processing-example {
    .steps-grid {
      grid-template-columns: 1fr;
    }
  }
}
