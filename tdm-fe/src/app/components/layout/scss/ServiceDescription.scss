@import "../../../scss/colors";

.service-description {
  padding: 3rem 0;
  background-color: #f5f5f5;

  h2 {
    margin-bottom: 1.5rem;
    font-size: 2rem;
    font-weight: 600;
    line-height: 1.3;
    color: #333;
  }

  .description-text {
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    line-height: 1.6;
    color: #444;

    a {
      color: var(--dark-blue);
      text-decoration: underline;
      transition: color .2s ease;

      &:hover,
      &:focus {
        color: var(--very-dark-blue);
      }
    }

    em {
      font-style: italic;
      color: #666;
    }
  }

  .steps-container {
    display: flex;
    gap: 24px;
    justify-content: space-between;
    margin-bottom: 32px;

    .step {
      flex: 1;
      padding: 24px;
      text-align: left;
      background: #fff;
      box-shadow: 0 2px 4px rgba(0, 0, 0, .1);

      .icon {
        width: 25px;
        height: 25px;
        font-size: 25px;
        color: #1a5a71;
      }

      h3 {
        margin-bottom: 12px;
        font-size: 18px;
        color: $blue;
      }

      p {
        font-size: 14px;
        color: #666;
      }

      a {
        color: var(--dark-blue);
        text-decoration: none;
        transition: color .2s ease;

        &:hover {
          color: var(--very-dark-blue);
          text-decoration: underline;
        }
      }
    }
  }

  .start-button {
    display: flex;
    justify-content: center;
    text-decoration: none;

    button {
      padding: 12px 24px;
      color: #fff;
      text-transform: none;
      background-color: $blue;
      border-radius: 5px;

      &:hover {
        background-color: var(--blue);
      }
    }
  }

  @media (max-width: 768px) {
    padding: 2rem 0;

    h2 {
      font-size: 1.5rem;
    }

    .description-text {
      font-size: 1rem;
    }
  }
}

