@import "../../scss/colors";

.home-page {
  max-width: 1200px;
  padding: 48px 24px;

  h1 {
    margin-top: 0;
    margin-bottom: 40px;
    font-size: 40px;
    font-weight: 400;
    line-height: 1.3;
    color: $light-black;
  }

  h2 {
    margin-bottom: 30px;
    font-size: 30px;
    font-weight: 700;
    color: $light-black;
  }

  .choices-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
  }
}

.choice-card {
  position: relative;
  height: 250px;
  overflow: visible;
  cursor: pointer;
  background-color: #1a5a71;
  border: none;
  border-radius: 12px;
  transition: transform .2s ease-in-out;

  &:nth-child(2) {
    background-color: #5ba3bc;
  }

  &:hover {
    transform: translateY(-4px);
  }
}

.card-content {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 30px;
}

.icon-container {
  position: absolute;
  top: 30px;
  left: 30px;

  .card-icon {
    width: 80px;
    height: auto;
    color: #fff;
  }
}

.bottom-container {
  position: absolute;
  right: 30px;
  bottom: 100px;
  left: 30px;
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
}

.title-text {
  max-width: 70%;
  font-size: 19px;
  font-weight: 700;
  line-height: 1.4;
  color: #fff;
}

.start-button {
  font-size: 15px;
  color: #fff;
  text-decoration: underline;
  cursor: pointer;
}

@media (max-width: 768px) {
  .home-page {
    padding: 24px 16px;

    h1 {
      font-size: 24px;
    }

    .choices-container {
      grid-template-columns: 1fr;
    }
  }
}
